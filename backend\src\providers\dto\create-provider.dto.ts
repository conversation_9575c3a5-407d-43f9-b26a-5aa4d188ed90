import { IsString, <PERSON>Enum, IsObject, IsArray, IsOptional, IsBoolean, IsNumber } from 'class-validator';
import { ProviderType, ProviderStatus } from '../../../../shared/types';

export class CreateProviderDto {
  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(ProviderType)
  type!: ProviderType;

  @IsObject()
  configuration!: Record<string, any>;

  @IsArray()
  @IsString({ each: true })
  models!: string[];

  @IsArray()
  @IsString({ each: true })
  capabilities!: string[];

  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsNumber()
  reliability?: number;

  @IsOptional()
  @IsNumber()
  costPerToken?: number;

  @IsOptional()
  @IsNumber()
  averageLatency?: number;

  @IsOptional()
  @IsNumber()
  priority?: number;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}